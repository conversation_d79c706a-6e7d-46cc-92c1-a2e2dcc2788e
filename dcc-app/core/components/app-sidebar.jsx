import { useState } from "react";
import {
  Calendar,
  Home,
  Inbox,
  Search,
  Settings,
  LayoutDashboard,
  Waypoints,
  SquareUser,
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  UserSquare,
  UserCog,
  Cog,
  User,
  Workflow,
  BookUser,
  SquareLibrary,
  GraduationCap,
  Store,
} from "lucide-react";

import { useSidebar } from "@core/components/ui/sidebar";
import { TeamSwitcher } from "@core/components/ui/team-switcher";
import { NavMain } from "@core/components/ui/nav-main";
import { NavProjects } from "@core/components/ui/nav-projects";
import { NavRole } from "@core/components/ui/nav-role";
import { NavDashboard } from "@core/components/ui/nav-dashboard";
import { NavResources } from "@core/components/ui/nav-resources";
import { NavUser } from "@core/components/ui/nav-user";
import { But<PERSON> } from "@core/components/ui/button";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@core/components/ui/select";

// Menu items.
const items = [
  {
    title: "Skills Library",
    url: "#",
    icon: LayoutDashboard,
    isActive: true,
    items: [
      {
        title: "History",
        url: "#",
      },
      {
        title: "Starred",
        url: "#",
      },
      {
        title: "Settings",
        url: "#",
      },
    ],
  },
];

const data = {
  user: {
    name: "Richard Stephens",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  // teams: [
  //   {
  //     name: "Acme Inc",
  //     logo: GalleryVerticalEnd,
  //     plan: "Enterprise",
  //   },
  //   {
  //     name: "Acme Corp.",
  //     logo: AudioWaveform,
  //     plan: "Startup",
  //   },
  //   {
  //     name: "Evil Corp.",
  //     logo: Command,
  //     plan: "Free",
  //   },
  // ],

  dashboard: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
  ],

  navMain2: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
  ],

  navMain: [
    {
      title: "Behavioural skills",
      url: "#",
      icon: UserSquare,
      isActive: false,
      items: [
        {
          title: "Strategic Thinking",
          url: "/strategic-thinking",
        },
        {
          title: "Purposeful Planning",
          url: "#",
        },
        {
          title: "Shaping Solutions",
          url: "#",
        },
        {
          title: "Customer Focus",
          url: "#",
        },
        {
          title: "Agile and Adaptable",
          url: "#",
        },
        {
          title: "Engage and Influence",
          url: "#",
        },
        {
          title: "Deliver Results",
          url: "#",
        },
        {
          title: "Collaborate Openly",
          url: "#",
        },
        {
          title: "Trust and Integrity",
          url: "#",
        },
        {
          title: "Develop Self",
          url: "#",
        },
        {
          title: "Enable Performance",
          url: "#",
        },
        {
          title: "Develop Others",
          url: "#",
        },
      ],
    },
    {
      title: "Technical skills",
      url: "#",
      icon: Cog,
      items: [
        {
          title: "Business Continuity and Disaster Recovery",
          url: "/business-continuity",
        },
        {
          title: "Data Analytics and Insights ",
          url: "#",
        },
        {
          title: "Programme Management",
          url: "#",
        },
        {
          title: "Certificate Management",
          url: "#",
        },
        {
          title: "Development Lifecycle ",
          url: "#",
        },
        {
          title: "Risk Management",
          url: "#",
        },
        {
          title: "Change Management",
          url: "#",
        },
        {
          title: "Enterprise System Architecture",
          url: "#",
        },
        {
          title: "Supplier Management",
          url: "#",
        },
        {
          title: "Commercial Acumen",
          url: "#",
        },
        {
          title: "External Environment Scanning",
          url: "#",
        },
        {
          title: "Sustainability and Environment",
          url: "#",
        },
        {
          title: "Compliance and Regulatory Assurance",
          url: "#",
        },
        {
          title: "Governance ",
          url: "#",
        },
        {
          title: "Talent Management",
          url: "#",
        },
        {
          title: "Communications ",
          url: "#",
        },
        {
          title: "Incident Response Lifecycle",
          url: "#",
        },
        {
          title: "Technology Assurance and Testing",
          url: "#",
        },
        {
          title: "Continuous Improvement",
          url: "#",
        },
        {
          title: "Infrastructure and Cloud Computing",
          url: "#",
        },
        {
          title: "Threat Intelligence",
          url: "#",
        },
        {
          title: "Contract Management ",
          url: "#",
        },
        {
          title: "Legal Counsel",
          url: "#",
        },
        {
          title: "Customer Experience Design",
          url: "#",
        },
        {
          title: "Portfolio Management",
          url: "#",
        },
        {
          title: "Customer Operations ",
          url: "#",
        },
        {
          title: "Procurement",
          url: "#",
        },
        {
          title: "Critical Thinking ",
          url: "#",
        },
        {
          title: "Project Management",
          url: "#",
        },
      ],
    },
    // {
    //   title: "Documentation",
    //   url: "#",
    //   icon: BookOpen,
    //   items: [
    //     {
    //       title: "Introduction",
    //       url: "#",
    //     },
    //     {
    //       title: "Get Started",
    //       url: "#",
    //     },
    //     {
    //       title: "Tutorials",
    //       url: "#",
    //     },
    //     {
    //       title: "Changelog",
    //       url: "#",
    //     },
    //   ],
    // },
    //   {
    //     title: "Settings",
    //     url: "#",
    //     icon: Settings2,
    //     items: [
    //       {
    //         title: "General",
    //         url: "#",
    //       },
    //       {
    //         title: "Team",
    //         url: "#",
    //       },
    //       {
    //         title: "Billing",
    //         url: "#",
    //       },
    //       {
    //         title: "Limits",
    //         url: "#",
    //       },
    //     ],
    //   },
  ],
  projects: [
    // {
    //   name: "Function",
    //   url: "#",
    //   icon: Workflow,
    // },
    // {
    //   name: "Owner",
    //   url: "#",
    //   icon: User,
    // },
  ],
  role: [
    {
      name: "Roles",
      url: "/roles",
      icon: BookUser,
    },
  ],
  resources: [
    {
      name: "Talent Marketplace",
      url: "/talent-marketplace",
      icon: Store,
    },
    // {
    //   name: "Interview bank",
    //   url: "#",
    //   icon: SquareLibrary,
    // },
    // {
    //   name: "Learning resources",
    //   url: "#",
    //   icon: GraduationCap,
    // },
  ],
};

const algorithm = [
  "Cyber Security Operations",
  "Security Architecture and Assurance",
];
const language = [
  "Security Compliance, Risk and Resilience",
  "Security, Demand, Capability and Awareness",
];

export function AppSidebar(userData) {
  const {
    state,
    open,
    setOpen,
    openMobile,
    setOpenMobile,
    isMobile,
    toggleSidebar,
  } = useSidebar();

  const [selected, setSelected] = useState("");
  const [teamSelected, setTeamSelected] = useState(false);
  const [showSkills, setShowSkills] = useState(false);
  const [typeSelected, setTypeSelected] = useState("");

  const changeSelectOptionHandler = (value) => {
    setSelected(value);
    setShowSkills(false);
  };

  const teamSelectOptionHandler = (value) => {
    setTeamSelected(value);
    setShowSkills(true);
  };

  const typeSelector = (value) => {
    props.handleShowSkills(value);
  };

  // const typeSelectOptionHandler = (event) => {
  //   setTypeSelected(event.target.value);
  // };

  // console.log("selected");
  // console.log(selected);

  // console.log("teamSelected");
  // console.log(teamSelected);

  /** Type variable to store different array for different dropdown */
  let type = null;

  /** This will be used to create set of options that user will see */
  let options = null;

  /** Setting Type variable according to dropdown */
  if (selected === "Security") {
    type = algorithm;
  } else if (selected === "Another Security") {
    type = language;
  }

  /** If "Type" is null or undefined then options will be null,
   * otherwise it will create a options iterable based on our array
   */
  if (type) {
    options = type.map((el) => (
      <SelectItem key={el} value={el}>
        {el}
      </SelectItem>
    ));
  }

  return (
    <Sidebar collapsible="offcanvas">
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup />
        <NavDashboard projects={data.dashboard} />
        <NavMain items={data.navMain} />
        {/* <NavProjects projects={data.projects} /> */}
        <SidebarGroup />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
