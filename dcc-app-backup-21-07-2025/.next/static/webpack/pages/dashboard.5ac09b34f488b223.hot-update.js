"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/LoginForm.js":
/*!***************************!*\
  !*** ./core/LoginForm.js ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// import {y\n//   InputOTP,\n//   InputOTPGroup,\n//   InputOTPSeparator,\n//   InputOTPSlot,\n// } from \"@core/components/ui/input-otp\";\nfunction LoginForm() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [passCode, setPassCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPasscode, setshowPasscode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errorOnSubmit, setErrorOnSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    /* --- DEBUG --- */ console.log(\"session\");\n    console.log(session);\n    console.log(\"userData\");\n    console.log(userData);\n    /* --- DEBUG --- */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"LoginForm.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                }\n            }[\"LoginForm.useEffect\"]);\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"LoginForm.useEffect\": (_event, session)=>{\n                    setSession(session);\n                }\n            }[\"LoginForm.useEffect\"]);\n        }\n    }[\"LoginForm.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            let mounted = true;\n            getUserProfile();\n            return function cleanup() {\n                mounted = false;\n            };\n        }\n    }[\"LoginForm.useEffect\"], [\n        session\n    ]);\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n        // if (data.user) {\n        //   router.push(\"/dashboard\");\n        //   // setUserData(data);\n        // }\n        } catch (error) {\n            console.log(\"user not logged in\");\n            setLoading(false);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    const handleLogin = async (email)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOtp({\n                email: email,\n                options: {\n                    // set this to false if you do not want the user to be automatically signed up\n                    shouldCreateUser: false\n                }\n            });\n            if (error) {\n                throw error;\n            } else {\n                // console.log(data);\n                setshowPasscode(true);\n            // router.push(\"/dashboard\");\n            }\n        } catch (error) {\n        // console.log(error.error_description || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendPasscode = async ()=>{\n        try {\n            setLoading(true);\n            const { data: { session }, error } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.verifyOtp({\n                email: '<EMAIL>',\n                token: passCode,\n                type: \"email\"\n            });\n            if (error) {\n                throw error;\n            } else {\n            // console.log(session);\n            }\n        } catch (error) {\n            // console.log(error.error_description || error.message);\n            setErrorOnSubmit(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col justify-center py-3 sm:px-6 lg:px-8\",\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid place-items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__.Oval, {\n                    stroke: \"#0c39ac\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 137,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                lineNumber: 136,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n            lineNumber: 135,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto flex justify-center p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                src: \"/logo/dcc-logo.svg\",\n                                alt: \"DCC\",\n                                width: 250,\n                                height: 100\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-Ubuntu mt-6 text-center text-2xl font-extrabold text-gray-900\",\n                            children: \"Sign into your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid place-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__.Oval, {\n                                    stroke: \"#0c39ac\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 160,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: showPasscode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid place-items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row space-x-2 mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Please check your email for a passcode\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 27\n                                                }, this),\n                                                \" and enter it here to login\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-sm min-w-[20px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    value: passCode || \"\",\n                                                    onChange: (e)=>setPassCode(e.target.value),\n                                                    type: \"passcode\",\n                                                    maxLength: 6,\n                                                    required: true,\n                                                    className: \"w-full bg-transparent placeholder:text-slate-400 text-slate-700 text-lg border border-slate-200 rounded-md pl-3 py-2 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-300 shadow-sm focus:shadow\",\n                                                    placeholder: \"passcode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-2 top-2 rounded bg-primary py-1 px-2.5 border border-transparent text-center text-sm text-white transition-all shadow-sm hover:shadow focus:bg-green-500 focus:shadow-none active:bg-green-700 hover:bg-green-700 active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none\",\n                                                    type: \"button\",\n                                                    onClick: sendPasscode,\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 23\n                                    }, this),\n                                    errorOnSubmit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-error pt-6\",\n                                        children: errorOnSubmit\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 25\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 168,\n                                columnNumber: 21\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Email address\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Your email\",\n                                                    value: email || \"\",\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 229,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    handleLogin(email);\n                                                },\n                                                disabled: loading,\n                                                className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                                children: \"Sign in\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-0 flex flex-row space-x-2\",\n                                                children: !errorOnSubmit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-error pt-6\",\n                                                    children: errorOnSubmit\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 29\n                                                }, this) : null\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 240,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 221,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 166,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"PViyHEhlMOeRf108W3y/RH1dhI8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/LoginForm.js\n"));

/***/ })

});