"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/LoginForm.js":
/*!***************************!*\
  !*** ./core/LoginForm.js ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// import {y\n//   InputOTP,\n//   InputOTPGroup,\n//   InputOTPSeparator,\n//   InputOTPSlot,\n// } from \"@core/components/ui/input-otp\";\nfunction LoginForm() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [passCode, setPassCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPasscode, setshowPasscode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [errorOnSubmit, setErrorOnSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    /* --- DEBUG --- */ console.log(\"session\");\n    console.log(session);\n    console.log(\"userData\");\n    console.log(userData);\n    /* --- DEBUG --- */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"LoginForm.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                }\n            }[\"LoginForm.useEffect\"]);\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"LoginForm.useEffect\": (_event, session)=>{\n                    setSession(session);\n                }\n            }[\"LoginForm.useEffect\"]);\n        }\n    }[\"LoginForm.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            let mounted = true;\n            getUserProfile();\n            return function cleanup() {\n                mounted = false;\n            };\n        }\n    }[\"LoginForm.useEffect\"], [\n        session\n    ]);\n    async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n        // if (data.user) {\n        //   router.push(\"/dashboard\");\n        //   // setUserData(data);\n        // }\n        } catch (error) {\n            console.log(\"user not logged in\");\n            setLoading(false);\n        } finally{\n        // setLoading(false);\n        }\n    }\n    const handleLogin = async (email)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOtp({\n                email: email,\n                options: {\n                    // set this to false if you do not want the user to be automatically signed up\n                    shouldCreateUser: false\n                }\n            });\n            if (error) {\n                throw error;\n            } else {\n                // console.log(data);\n                setshowPasscode(true);\n                router.push(\"/dashboard\");\n            }\n        } catch (error) {\n        // console.log(error.error_description || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendPasscode = async ()=>{\n        try {\n            setLoading(true);\n            const { data: { session }, error } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.verifyOtp({\n                email: email,\n                token: passCode,\n                type: \"email\"\n            });\n            if (error) {\n                throw error;\n            } else {\n            // console.log(session);\n            }\n        } catch (error) {\n            // console.log(error.error_description || error.message);\n            setErrorOnSubmit(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col justify-center py-3 sm:px-6 lg:px-8\",\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid place-items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__.Oval, {\n                    stroke: \"#0c39ac\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 137,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                lineNumber: 136,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n            lineNumber: 135,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto flex justify-center p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                src: \"/logo/dcc-logo.svg\",\n                                alt: \"DCC\",\n                                width: 250,\n                                height: 100\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-Ubuntu mt-6 text-center text-2xl font-extrabold text-gray-900\",\n                            children: \"Sign into your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid place-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_3__.Oval, {\n                                    stroke: \"#0c39ac\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 160,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: showPasscode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid place-items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row space-x-2 mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Please check your email for a passcode\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 27\n                                                }, this),\n                                                \" and enter it here to login\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-sm min-w-[20px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    value: passCode || \"\",\n                                                    onChange: (e)=>setPassCode(e.target.value),\n                                                    type: \"passcode\",\n                                                    maxLength: 6,\n                                                    required: true,\n                                                    className: \"w-full bg-transparent placeholder:text-slate-400 text-slate-700 text-lg border border-slate-200 rounded-md pl-3 py-2 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-300 shadow-sm focus:shadow\",\n                                                    placeholder: \"passcode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute right-2 top-2 rounded bg-primary py-1 px-2.5 border border-transparent text-center text-sm text-white transition-all shadow-sm hover:shadow focus:bg-green-500 focus:shadow-none active:bg-green-700 hover:bg-green-700 active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none\",\n                                                    type: \"button\",\n                                                    onClick: sendPasscode,\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 23\n                                    }, this),\n                                    errorOnSubmit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-error pt-6\",\n                                        children: errorOnSubmit\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 25\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 168,\n                                columnNumber: 21\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Email address\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Your email\",\n                                                    value: email || \"\",\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    required: true,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 229,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    handleLogin(email);\n                                                },\n                                                disabled: loading,\n                                                className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                                children: \"Sign in\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-0 flex flex-row space-x-2\",\n                                                children: !errorOnSubmit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-error pt-6\",\n                                                    children: errorOnSubmit\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 29\n                                                }, this) : null\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                        lineNumber: 240,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                                lineNumber: 221,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                            lineNumber: 166,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/LoginForm.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"2xpYR29PeUzRhlb8YsyUuqAMseM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/LoginForm.js\n"));

/***/ })

});